<!DOCTYPE html>
<html lang="nl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SuppRadar - Supplementen</title>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="supplementen.css">
  <style>
    /* Basis styling voor de filter-secties – achtergrondkleuren komen uit supplementen.css */
    .filter-section { margin-bottom: 20px; }
    .filter-section h3 {
      margin-bottom: 10px;
      font-size: 1.1em;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }
    .filter-section ul { list-style: none; padding: 0; margin: 0; }
    .filter-section ul li { margin-bottom: 8px; }
    /* Prijsvierkanten en inputvelden */
    .price-boxes { display: flex; gap: 10px; margin-bottom: 10px; }
    .price-box input[type="number"] {
      width: 50px; border: none; outline: none;
      text-align: right; font-size: 1em; font-weight: bold;
      background: #fff; padding: 0;
      /* Pijltjes zichtbaar maken */
      -moz-appearance: textfield; /* Firefox */
    }
    /* Pijltjes styling voor Chrome/Safari */
    .price-box input[type="number"]::-webkit-inner-spin-button {
      opacity: 1;
      margin-right: 2px;
    }
    /* Euroteken verwijderd (titel toont nu "Prijs (€)") */
    #min-price-box span.currency, #max-price-box span.currency { display: none; }
    #min-price-box, #max-price-box {
      width: 60px; height: 60px; border: 1px solid #ccc;
      display: flex; align-items: center; justify-content: center;
      background: #fff; font-weight: bold;
    }
    /* Slider container en track */
    .price-slider-container { position: relative; height: 30px; }
    .price-slider-track {
      position: absolute; top: 50%; left: 0; right: 0;
      height: 4px; background: #ccc;
      transform: translateY(-50%);
      pointer-events: none; z-index: 1;
    }
    .slider-handle {
      position: absolute; top: 50%;
      width: 20px; height: 20px; background: #333;
      border-radius: 50%; transform: translate(-50%, -50%);
      cursor: pointer; z-index: 100;
      user-select: none; touch-action: none;
    }
    .reset-smooth {
      background-color: #ffdddd;
      transition: background-color 0.5s ease;
    }
    /* Resultaten en sortering */
    .results-sorting {
      display: flex; justify-content: space-between; align-items: center;
      margin-bottom: 10px;
    }
    .results-info { font-size: 1em; font-weight: bold; }
    .sort-menu { /* Houd de sortering op dezelfde hoogte */ }
    /* Sterren voor beoordeling */
    .star { font-size: 1.1em; vertical-align: middle; }
    .filled-star { color: #F05D23; }
    .empty-star { color: #777; }
    /* "Bekijk Meer" knop */
    .button { white-space: nowrap; }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo-container">
      <a href="./index.html" class="logo-link">
        <img src="./Afbeeldingen/logo.png" alt="SuppRadar Logo">
        <h1>SuppRadar.com</h1>
      </a>
    </div>
    <div class="nav-menu">
      <a href="./index.html">Home</a>
      <a href="./supplementen.html" class="active">Supplementen</a>
      <div class="dropdown">
        <a href="#">Shop per Doel</a>
        <div class="dropdown-content">
          <a href="#">Gewichtsverlies</a>
          <a href="#">Spieropbouw</a>
          <a href="#">Uithoudingsvermogen</a>
          <a href="#">Fitness & Onderhoud</a>
          <a href="#">Gezondheid & Focus</a>
          <a href="#">Topprestaties</a>
        </div>
      </div>
      <a href="#">Accessoires</a>
      <a href="#">Oefeningen</a>
      <a href="#">Blog</a>
    </div>
  </div>
  
  <main class="content-container">
    <aside class="filter-menu">
      <!-- Categorieën -->
      <div class="filter-section categories">
        <h3>Categorieën</h3>
        <ul>
          <li>
            <label>
              <input type="checkbox" name="category" value="Eiwitten"> Eiwitten 
              <span class="category-count" data-category="Eiwitten">(0)</span>
            </label>
          </li>
          <li>
            <label>
              <input type="checkbox" name="category" value="Pre-workout"> Pre-workout 
              <span class="category-count" data-category="Pre-workout">(0)</span>
            </label>
          </li>
          <li>
            <label>
              <input type="checkbox" name="category" value="Creatine"> Creatine 
              <span class="category-count" data-category="Creatine">(0)</span>
            </label>
          </li>
          <li>
            <label>
              <input type="checkbox" name="category" value="Vitamines"> Vitamines 
              <span class="category-count" data-category="Vitamines">(0)</span>
            </label>
          </li>
        </ul>
      </div>
      <!-- Prijsfilter -->
      <div class="filter-section price">
        <h3>Prijs (€)</h3>
        <div class="price-boxes">
          <div id="min-price-box" class="price-box">
            <input type="number" id="min-price-input" step="1" min="0" placeholder="">
          </div>
          <div id="max-price-box" class="price-box">
            <input type="number" id="max-price-input" step="1" min="1" placeholder="">
          </div>
        </div>
        <div class="price-slider-container">
          <div id="price-slider-track" class="price-slider-track"></div>
          <div id="min-handle" class="slider-handle"></div>
          <div id="max-handle" class="slider-handle"></div>
        </div>
        <input type="range" id="min-price-range" min="0" value="0" step="1" style="display: none;">
        <input type="range" id="max-price-range" min="1" value="0" step="1" style="display: none;">
      </div>
      <!-- Beoordelingsfilter -->
      <div class="filter-section ratings">
        <h3>Minimale beoordeling</h3>
        <ul>
          <li>
            <label>
              <input type="radio" name="rating" value="0" checked>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
            </label>
          </li>
          <li>
            <label>
              <input type="radio" name="rating" value="1">
              <span class="star filled-star">&#9733;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
            </label>
          </li>
          <li>
            <label>
              <input type="radio" name="rating" value="2">
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
            </label>
          </li>
          <li>
            <label>
              <input type="radio" name="rating" value="3">
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star empty-star">&#9734;</span>
              <span class="star empty-star">&#9734;</span>
            </label>
          </li>
          <li>
            <label>
              <input type="radio" name="rating" value="4">
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star empty-star">&#9734;</span>
            </label>
          </li>
          <li>
            <label>
              <input type="radio" name="rating" value="5">
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
              <span class="star filled-star">&#9733;</span>
            </label>
          </li>
        </ul>
      </div>
    </aside>
    
    <section class="product-table-container">
      <div class="results-sorting">
        <div class="results-info">
          <span id="results-count">0</span> resultaten
        </div>
        <div class="sort-menu">
          <span>Sortering: </span>
          <select id="sort-menu">
            <option value="default" selected>Relevantie</option>
            <option value="price-low-high">Prijs: Laag naar Hoog</option>
            <option value="price-high-low">Prijs: Hoog naar Laag</option>
            <option value="rating">Beoordeling</option>
          </select>
        </div>
      </div>
      
      <table class="product-table">
        <thead>
          <tr>
            <th>Afbeelding</th>
            <th>Naam</th>
            <th>Prijs</th>
            <th>Beoordeling</th>
            <th>Details</th>
            <th>Actie</th>
          </tr>
        </thead>
        <tbody>
          <tr data-category="Eiwitten" data-price="25.99" data-rating="4.5">
            <td><img src="./Afbeeldingen/Whey.png" alt="Whey Proteïne"></td>
            <td>Whey Proteïne</td>
            <td>€26</td>
            <td>⭐⭐⭐⭐☆</td>
            <td>Beschikbare smaken: Chocolade, Vanille</td>
            <td><a href="./whey.html" class="button">Bekijk Meer</a></td>
          </tr>
          <tr data-category="Pre-workout" data-price="29.99" data-rating="5.0">
            <td><img src="./Afbeeldingen/Pre.png" alt="Pre-Workout"></td>
            <td>Pre-Workout Booster</td>
            <td>€30</td>
            <td>⭐⭐⭐⭐⭐</td>
            <td>Beschikbare smaken: Aardbei, Watermeloen</td>
            <td><a href="./preworkout.html" class="button">Bekijk Meer</a></td>
          </tr>
          <tr data-category="Creatine" data-price="19.99" data-rating="4.3">
            <td><img src="./Afbeeldingen/Creatine.png" alt="Creatine Monohydraat"></td>
            <td>Creatine Monohydraat</td>
            <td>€20</td>
            <td>⭐⭐⭐⭐☆</td>
            <td>Beschikbare smaken: Neutrale smaak</td>
            <td><a href="./creatine.html" class="button">Bekijk Meer</a></td>
          </tr>
          <tr data-category="Vitamines" data-price="15.99" data-rating="4.0">
            <td><img src="./Afbeeldingen/Vitamines.png" alt="Multivitaminen"></td>
            <td>Multivitaminen</td>
            <td>€16</td>
            <td>⭐⭐⭐⭐☆</td>
            <td>Beschikbare vormen: Tabletvorm</td>
            <td><a href="./multivitaminen.html" class="button">Bekijk Meer</a></td>
          </tr>
        </tbody>
      </table>
    </section>
  </main>
  
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const sortMenu = document.getElementById('sort-menu');
      const tbody = document.querySelector('.product-table tbody');
      const categoryCheckboxes = document.querySelectorAll('input[name="category"]');
      const ratingRadios = document.querySelectorAll('input[name="rating"]');
      
      // Elementen voor de prijsfilter
      const minPriceInputField = document.getElementById('min-price-input');
      const minPriceRange = document.getElementById('min-price-range');
      const maxPriceInputRange = document.getElementById('max-price-range');
      const maxPriceInputField = document.getElementById('max-price-input');
      const minHandle = document.getElementById('min-handle');
      const maxHandle = document.getElementById('max-handle');
      const sliderTrack = document.getElementById('price-slider-track');
      
      // Flags
      let maxPriceEditing = false;
      let manualMaxValue = null;
      let hasManualChanged = false;
      
      function noFiltersApplied() {
        const anyCategory = Array.from(categoryCheckboxes).some(cb => cb.checked);
        const ratingValue = parseFloat(document.querySelector('input[name="rating"]:checked').value);
        return (!anyCategory && ratingValue === 0);
      }
      
      // Aangepaste input handlers voor pijltjes
      if (maxPriceInputField) {
        maxPriceInputField.addEventListener('focus', () => { 
          maxPriceEditing = true; 
        });
        
        maxPriceInputField.addEventListener('input', function() {
          if (this.value === "") {
            hasManualChanged = false;
            manualMaxValue = null;
          } else {
            hasManualChanged = true;
            manualMaxValue = Math.round(parseFloat(this.value));
            if (!isNaN(manualMaxValue)) {
              this.value = manualMaxValue;
              maxPriceInputRange.value = manualMaxValue;
              updateSliderUI();
              filterProducts();
            }
          }
        });
        
        maxPriceInputField.addEventListener('change', function() {
          if (!isNaN(parseFloat(this.value))) {
            manualMaxValue = Math.round(parseFloat(this.value));
            maxPriceInputRange.value = manualMaxValue;
            updateSliderUI();
            filterProducts();
          }
          maxPriceEditing = false;
        });
        
        maxPriceInputField.addEventListener('blur', function() {
          maxPriceEditing = false;
          if (!hasManualChanged || this.value === "") {
            updateSliderMax();
          }
          updateSliderUI();
        });
        
        // Pijltjes event listeners
        maxPriceInputField.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            e.preventDefault();
            let currentValue = parseInt(this.value) || 0;
            if (e.key === 'ArrowUp') currentValue++;
            if (e.key === 'ArrowDown') currentValue--;
            
            // Zorg dat de waarde binnen de grenzen blijft
            const minValue = parseInt(minPriceInputField.value) || 0;
            const maxPossible = parseInt(maxPriceInputRange.max);
            currentValue = Math.max(minValue, Math.min(maxPossible, currentValue));
            
            this.value = currentValue;
            manualMaxValue = currentValue;
            maxPriceInputRange.value = currentValue;
            hasManualChanged = true;
            updateSliderUI();
            filterProducts();
          }
        });
      }
      
      // Min price input met pijltjes ondersteuning
      minPriceInputField.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
          e.preventDefault();
          let currentValue = parseInt(this.value) || 0;
          if (e.key === 'ArrowUp') currentValue++;
          if (e.key === 'ArrowDown') currentValue--;
          
          // Zorg dat de waarde binnen de grenzen blijft
          const maxValue = parseInt(maxPriceInputField.value) || parseInt(maxPriceInputRange.max);
          currentValue = Math.max(0, Math.min(maxValue, currentValue));
          
          this.value = currentValue === 0 ? "" : currentValue;
          minPriceRange.value = currentValue;
          updateSliderUI();
          filterProducts();
        }
      });

      const allRows = Array.from(tbody.querySelectorAll('tr'));
      const originalPrices = allRows.map(row => parseFloat(row.dataset.price));
      const globalMax = Math.max(...originalPrices);
      
      // Initialisatie
      maxPriceInputRange.max = globalMax;
      maxPriceInputRange.value = globalMax;
      if (maxPriceInputField) {
        maxPriceInputField.value = globalMax;
        maxPriceInputField.setAttribute("max", globalMax);
      }
      minPriceRange.max = globalMax;
      minPriceRange.value = 0;
      minPriceInputField.value = "";
      
      function updateResultsCount() {
        const visibleRows = allRows.filter(row => row.style.display !== "none");
        document.getElementById('results-count').textContent = visibleRows.length;
      }
      
      function filterNonPrice() {
        const selectedCategories = Array.from(categoryCheckboxes)
          .filter(cb => cb.checked)
          .map(cb => cb.value);
        const selectedRating = parseFloat(document.querySelector('input[name="rating"]:checked').value);
        
        allRows.forEach(row => {
          let show = true;
          if (selectedCategories.length > 0 && !selectedCategories.includes(row.dataset.category)) {
            show = false;
          }
          if (parseFloat(row.dataset.rating) < selectedRating) {
            show = false;
          }
          row.style.display = show ? '' : 'none';
        });
      }
      
      function updateSliderMax() {
        if (maxPriceEditing) return;
        filterNonPrice();
        const visibleRows = allRows.filter(row => row.style.display !== "none");
        let newCalculatedMax = globalMax;
        
        if (visibleRows.length > 0) {
          newCalculatedMax = Math.ceil(Math.max(...visibleRows.map(row => parseFloat(row.dataset.price))));
        }
        
        const currentMin = parseFloat(minPriceRange.value) || 0;
        if (newCalculatedMax < currentMin) {
          newCalculatedMax = currentMin;
        }
        
        maxPriceInputRange.max = newCalculatedMax;
        
        if (noFiltersApplied()) {
          manualMaxValue = globalMax;
          hasManualChanged = false;
        } else {
          if (manualMaxValue === null || !hasManualChanged) {
            manualMaxValue = newCalculatedMax;
          } else if (manualMaxValue > newCalculatedMax) {
            manualMaxValue = newCalculatedMax;
          }
        }
        
        if (!maxPriceEditing) {
          maxPriceInputRange.value = manualMaxValue;
          if (maxPriceInputField) {
            maxPriceInputField.value = manualMaxValue;
          }
        }
        updateSliderUI();
      }
      
      function updateSliderUI() {
        const sliderWidth = sliderTrack.offsetWidth;
        const currentMax = parseFloat(maxPriceInputRange.max);
        const minVal = parseFloat(minPriceRange.value) || 0;
        const maxVal = parseFloat(maxPriceInputRange.value);
        const minPercent = minVal / currentMax;
        const maxPercent = maxVal / currentMax;
        
        minHandle.style.left = (minVal === 0 ? 0 : minPercent * sliderWidth - minHandle.offsetWidth / 2) + "px";
        maxHandle.style.left = (maxVal === currentMax ? sliderWidth : maxPercent * sliderWidth - maxHandle.offsetWidth / 2) + "px";
        
        if (maxPriceInputField && !maxPriceEditing) {
          maxPriceInputField.value = maxPriceInputRange.value;
        }
      }
      
      function filterProducts() {
        updateSliderMax();
        const selectedCategories = Array.from(categoryCheckboxes)
          .filter(cb => cb.checked)
          .map(cb => cb.value);
        const minPrice = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
        const maxPrice = parseFloat(maxPriceInputRange.value) || globalMax;
        const selectedRating = parseFloat(document.querySelector('input[name="rating"]:checked').value);
        
        allRows.forEach(row => {
          const price = parseFloat(row.dataset.price);
          let show = true;
          
          if (selectedCategories.length > 0 && !selectedCategories.includes(row.dataset.category)) {
            show = false;
          }
          if (price < minPrice || price > maxPrice) {
            show = false;
          }
          if (parseFloat(row.dataset.rating) < selectedRating) {
            show = false;
          }
          row.style.display = show ? '' : 'none';
        });
        
        sortProducts();
        updateResultsCount();
      }
      
      function sortProducts() {
        const sortValue = sortMenu.value;
        allRows.sort((a, b) => {
          const priceA = parseFloat(a.dataset.price);
          const priceB = parseFloat(b.dataset.price);
          const ratingA = parseFloat(a.dataset.rating);
          const ratingB = parseFloat(b.dataset.rating);
          
          if (sortValue === 'price-low-high') return priceA - priceB;
          if (sortValue === 'price-high-low') return priceB - priceA;
          if (sortValue === 'rating') return ratingB - ratingA;
          return 0;
        });
        
        tbody.replaceChildren(...allRows);
      }
      
      // Slider interactie
      let activeHandle = null;
      
      function pointerMoveHandler(e) {
        if (!activeHandle) return;
        const sliderRect = sliderTrack.getBoundingClientRect();
        const sliderWidth = sliderRect.width;
        let newPercent = (e.clientX - sliderRect.left) / sliderWidth;
        newPercent = Math.max(0, Math.min(1, newPercent));
        
        const currentMax = parseFloat(maxPriceInputRange.max);
        if (activeHandle === minHandle) {
          let newValue = Math.round(newPercent * currentMax);
          const currentRangeMax = parseFloat(maxPriceInputRange.value);
          if (newValue > currentRangeMax) newValue = currentRangeMax;
          minPriceRange.value = newValue;
          minPriceInputField.value = newValue > 0 ? newValue : "";
        } else if (activeHandle === maxHandle) {
          let newValue = Math.round(newPercent * currentMax);
          const currentMin = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
          if (newValue < currentMin) newValue = currentMin;
          maxPriceInputRange.value = newValue;
          if (maxPriceInputField) {
            maxPriceInputField.value = newValue;
          }
          manualMaxValue = newValue;
          hasManualChanged = true;
        }
        
        updateSliderUI();
        filterProducts();
      }
      
      function pointerUpHandler() {
        activeHandle = null;
        window.removeEventListener('pointermove', pointerMoveHandler);
        window.removeEventListener('pointerup', pointerUpHandler);
      }
      
      minHandle.addEventListener('pointerdown', function(e) {
        activeHandle = minHandle;
        window.addEventListener('pointermove', pointerMoveHandler);
        window.addEventListener('pointerup', pointerUpHandler);
        e.preventDefault();
      });
      
      maxHandle.addEventListener('pointerdown', function(e) {
        activeHandle = maxHandle;
        window.addEventListener('pointermove', pointerMoveHandler);
        window.addEventListener('pointerup', pointerUpHandler);
        e.preventDefault();
      });
      
      // Overige event listeners
      window.addEventListener('resize', updateSliderUI);
      
      minPriceInputField.addEventListener('input', function() {
        let value = parseFloat(this.value) || 0;
        value = Math.max(0, value);
        const currentMax = parseFloat(maxPriceInputRange.value);
        if (value > currentMax) value = currentMax;
        this.value = value === 0 ? "" : value;
        minPriceRange.value = value;
        updateSliderUI();
        filterProducts();
      });
      
      maxPriceInputRange.addEventListener('input', function() {
        const minValue = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
        if (parseFloat(this.value) < minValue) {
          this.value = minValue;
        }
        updateSliderUI();
        filterProducts();
      });
      
      function updateCategoryCounts() {
        const counts = {};
        allRows.forEach(row => {
          const category = row.dataset.category;
          counts[category] = (counts[category] || 0) + 1;
        });
        
        document.querySelectorAll('.category-count').forEach(span => {
          const category = span.dataset.category;
          span.textContent = `(${counts[category] || 0})`;
        });
      }
      
      // Initialisatie
      updateCategoryCounts();
      sortMenu.addEventListener('change', sortProducts);
      categoryCheckboxes.forEach(cb => cb.addEventListener('change', filterProducts));
      ratingRadios.forEach(radio => radio.addEventListener('change', filterProducts));
      
      // Activeer categorie via URL parameter
      const params = new URLSearchParams(window.location.search);
      const queryCategory = params.get('category');
      if (queryCategory) {
        const checkbox = document.querySelector(`input[name="category"][value="${queryCategory}"]`);
        if (checkbox) {
          checkbox.checked = true;
          checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }
      
      // Eerste update
      updateSliderUI();
      filterProducts();
    });
</script>
</body>
</html>