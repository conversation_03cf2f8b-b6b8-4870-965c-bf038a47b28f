<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <title>SuppRadar - Ontdek de Wereld van Supplementen</title>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <a href="./index.html" class="logo-link">
                <img src="./Afbeeldingen/logo.png" alt="SuppRadar Logo">
                <h1>SuppRadar.com</h1>
            </a>
        </div>
        <div class="nav-menu">
            <a href="./index.html" class="active">Home</a>
            <a href="./supplementen.html">Supplementen</a>
            <div class="dropdown">
                <a href="#">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="#">Gewichtsverlies</a>
                    <a href="#">Spieropbouw</a>
                    <a href="#">Uithoudingsvermogen</a>
                    <a href="#">Fitness & Onderhoud</a>
                    <a href="#">Gezondheid & Focus</a>
                    <a href="#">Topprestaties</a>
                </div>
            </div>
            <a href="#">Accessoires</a>
            <a href="#">Oefeningen</a>
            <a href="#">Blog</a>
        </div>
    </div>

    <div class="content-container">
        <div class="main-content-wrapper">
            <div class="main-content">
                <h1>Boost je Fitnessreis met Doelgerichte Supplementen</h1>
                <p>Bij SuppRadar geloven we in het vinden van de beste supplementen die aansluiten bij jouw doelen, zonder dat we onze eigen producten promoten. Als onafhankelijke gids helpen we je om de topmerken en meest effectieve supplementen te ontdekken voor gewichtsverlies, spieropbouw, energieverbetering, en meer. We werken samen met betrouwbare leveranciers zodat jij de beste keuze kunt maken, gebaseerd op objectieve aanbevelingen en uitgebreide productvergelijkingen. Vertrouw op onze expertise om jouw fitnessreis te ondersteunen met eerlijke en betrouwbare informatie.</p>
                <a href="./supplementen.html" class="see-all-supplements">Bekijk Alle Supplementen</a>
            </div>
        </div>

        <div class="categories-section">
            <div class="see-categories">Bekijk Onze Categorieën</div>
            <div class="categories">
                <div class="category-box">
		<a href="./supplementen.html?category=Eiwitten" class="button">Bekijk Meer</a>
                        <img src="./Afbeeldingen/Whey.png" alt="Proteïne">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="./supplementen.html?category=Pre-workout">
                        <img src="./Afbeeldingen/Creatine.png" alt="Creatine">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="./supplementen.html?category=Creatine">
                        <img src="./Afbeeldingen/Pre.png" alt="Pre-Workout">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="./supplementen.html?category=Vitamines">
                        <img src="./Afbeeldingen/Vitamines.png" alt="Vitaminen & Supplementen">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="newsletter-section">
            <h2>Schrijf je in voor onze nieuwsbrief</h2>
            <p>Ontvang de laatste updates, exclusieve aanbiedingen en meer direct in je inbox.</p>
            <form action="#" method="post" class="newsletter-form">
                <input type="email" name="email" placeholder="Voer je e-mailadres in" required>
                <button type="submit">Inschrijven</button>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-container">
            <div class="footer-column">
                <h2>Over SuppRadar</h2>
                <p>SuppRadar is jouw onafhankelijke gids in de wereld van supplementen. Wij helpen je objectieve keuzes te maken voor je fitnessdoelen.</p>
            </div>
            <div class="footer-column">
                <h2>Handige Links</h2>
                <ul>
                    <li><a href="#">Privacybeleid</a></li>
                    <li><a href="#">Algemene Voorwaarden</a></li>
                    <li><a href="#">Affiliate Disclaimer</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h2>Contact</h2>
                <p>Email: <EMAIL></p>
            </div>
            <div class="footer-column">
                <h2>Social Media</h2>
                <div class="social-icons">
                    <a href="https://facebook.com" target="_blank">
                        <img src="./Afbeeldingen/facebook.png" alt="Facebook">
                    </a>
                    <a href="https://instagram.com" target="_blank">
                        <img src="./Afbeeldingen/instagram.png" alt="Instagram">
                    </a>
                    <a href="https://tiktok.com" target="_blank">
                        <img src="./Afbeeldingen/tiktok.png" alt="TikTok">
                    </a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; <span id="current-year"></span> SuppRadar.com - Alle rechten voorbehouden.</p>
        </div>
    </footer>

    <script>
        // Dynamisch huidige jaar toevoegen aan de footer
        document.addEventListener('DOMContentLoaded', function () {
            const yearElement = document.getElementById('current-year');
            if (yearElement) {
                yearElement.textContent = new Date().getFullYear();
            }
        });
    </script>
</body>
</html>
