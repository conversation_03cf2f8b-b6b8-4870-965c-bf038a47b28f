/* ===== BLOG PAGE STYLES ===== */

/* Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--loblolly) 0%, var(--granny-smith) 100%);
    padding: 80px 20px 60px;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.blog-hero h1 {
    color: var(--black-pearl);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    color: var(--river-bed);
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 0;
}

/* Main Container */
.blog-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Sidebar Styles */
.blog-sidebar {
    background: var(--white);
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-section h3 {
    color: var(--black-pearl);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
    padding-bottom: 8px;
}

/* Search Styles */
.search-container {
    display: flex;
    gap: 8px;
}

.search-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--loblolly);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--granny-smith);
}

.search-button {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-button:hover {
    background: var(--hover-button-color);
}

/* Filter Checkboxes */
.category-filters {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.95rem;
    color: var(--river-bed);
    transition: color 0.3s ease;
}

.filter-checkbox:hover {
    color: var(--black-pearl);
}

.filter-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--loblolly);
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--button-color);
    border-color: var(--button-color);
}

.filter-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

/* Sort Dropdown */
.sort-dropdown {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--loblolly);
    border-radius: 8px;
    background: var(--white);
    font-size: 0.95rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.sort-dropdown:focus {
    outline: none;
    border-color: var(--granny-smith);
}

/* Sidebar CTA */
.sidebar-cta {
    background: linear-gradient(135deg, var(--loblolly) 0%, var(--granny-smith) 100%);
    border-radius: 12px;
    padding: 25px;
    margin-top: 30px;
}

.cta-item {
    margin-bottom: 25px;
}

.cta-item:last-child {
    margin-bottom: 0;
}

.cta-item h4 {
    color: var(--black-pearl);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.cta-item p {
    color: var(--river-bed);
    font-size: 0.9rem;
    margin-bottom: 12px;
    line-height: 1.5;
}

.cta-button {
    display: inline-block;
    background: var(--button-color);
    color: var(--white);
    padding: 10px 16px;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: var(--hover-button-color);
    transform: translateY(-1px);
}

.cta-button.secondary {
    background: var(--nevada);
}

.cta-button.secondary:hover {
    background: var(--river-bed);
}

/* Main Content */
.blog-main {
    min-height: 600px;
}

.blog-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--loblolly);
}

.results-info {
    color: var(--nevada);
    font-weight: 600;
}

/* Blog Grid */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.blog-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(6, 32, 52, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(6, 32, 52, 0.15);
}

.blog-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--loblolly);
}

.blog-content {
    padding: 25px;
}

.blog-category {
    display: inline-block;
    background: var(--loblolly);
    color: var(--river-bed);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.blog-title {
    color: var(--black-pearl);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.4;
}

.blog-summary {
    color: var(--nevada);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.blog-date {
    color: var(--granny-smith);
    font-size: 0.85rem;
    font-weight: 600;
}

.blog-read-time {
    color: var(--granny-smith);
    font-size: 0.85rem;
}

.read-more-btn {
    background: var(--button-color);
    color: var(--white);
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.read-more-btn:hover {
    background: var(--hover-button-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.pagination-btn {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--hover-button-color);
}

.pagination-btn:disabled {
    background: var(--loblolly);
    color: var(--nevada);
    cursor: not-allowed;
}

.pagination-info {
    color: var(--nevada);
    font-weight: 600;
}

/* Newsletter CTA Section */
.newsletter-cta-section {
    background: linear-gradient(135deg, var(--black-pearl) 0%, var(--river-bed) 100%);
    padding: 60px 20px;
    text-align: center;
    margin-top: 60px;
}

.newsletter-cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-cta-section h2 {
    color: var(--white);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.newsletter-cta-section p {
    color: var(--loblolly);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

.newsletter-form {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.newsletter-button {
    background: var(--button-color);
    color: var(--white);
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter-button:hover {
    background: var(--granny-smith);
}

.newsletter-benefits {
    display: flex;
    justify-content: center;
    gap: 20px;
    color: var(--loblolly);
    font-size: 0.9rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .blog-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px 15px;
    }
    
    .blog-sidebar {
        order: 2;
        position: static;
        padding: 20px;
    }
    
    .blog-main {
        order: 1;
    }
    
    .blog-hero h1 {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .newsletter-form {
        flex-direction: column;
        gap: 12px;
    }
    
    .newsletter-benefits {
        flex-direction: column;
        gap: 8px;
    }
    
    .sidebar-cta {
        margin-top: 20px;
    }
}
