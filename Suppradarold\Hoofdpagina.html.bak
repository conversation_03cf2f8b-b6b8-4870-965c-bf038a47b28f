<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
    <title>SuppRadar - <PERSON><PERSON><PERSON> de Wereld van Supplementen</title>
    <style>
        :root {
            --black-pearl: #062034;
            --loblolly: #c5d1d5;
            --nevada: #64767e;
            --granny-smith: #7b949a;
            --river-bed: #495d62;
            --river-bed-alt: #44535c;
            --white: #ffffff;
            --button-color: #7b949a;
            --hover-button-color: #062034;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--white);
            color: var(--black-pearl);
            line-height: 1.8;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            max-width: 100%;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 0;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 36px;
            font-weight: 700;
            line-height: 1.2;
            color: var(--white);
        }

        p {
            font-size: 16px;
            margin-bottom: 20px;
            color: var(--white);
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .header {
            background-color: var(--white);
            padding: 15px 35px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: inherit;
        }

        .logo-container img {
            max-width: 110px;
            height: auto;
            margin-right: 15px;
        }

        .logo-container h1 {
            font-size: 30px;
            margin: 0;
            color: var(--black-pearl);
            font-weight: 700;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-menu a {
            color: var(--black-pearl);
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            transition: font-size 0.3s ease, border-bottom 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            font-size: 18px;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 50px; /* Dropdown komt 50px onder de knop */
            left: 0;
            background-color: var(--white);
            min-width: 180px;
            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
            z-index: 1;
            padding: 10px 0;
        }

        .dropdown-content a {
            color: var(--black-pearl);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-weight: 600;
            font-size: 16px;
            text-align: left;
        }

        .dropdown-content a:hover {
            background-color: var(--loblolly);
        }

        .dropdown:hover .dropdown-content,
        .dropdown-content:hover {
            display: block; /* Dropdown blijft zichtbaar als je erover beweegt */
        }

        .content-container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
        }

        .main-content-wrapper {
            background: linear-gradient(to right, var(--black-pearl), var(--river-bed-alt));
            padding: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .main-content {
            padding: 20px;
            max-width: 800px;
        }

        .main-content h1 {
            margin-bottom: 20px;
            font-size: 36px;
        }

        .main-content p {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .see-all-supplements {
            margin-top: 10px;
            padding: 12px 24px;
            background-color: var(--button-color);
            color: var(--white);
            font-weight: 700;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            transition: background-color 0.3s ease;
        }

        .see-all-supplements:hover {
            background-color: var(--hover-button-color);
        }

        .categories-section {
		padding: 20px 20px;
		margin-top: -200px; /* Verhoog deze waarde */
		background-color: var(--white);
		text-align: center;
		}

        .see-categories {
            font-size: 28px;
            font-weight: 700;
            color: var(--black-pearl);
            margin-bottom: 20px;
        }

        .categories {
            display: flex;
            justify-content: space-around;
            max-width: 1080px;
            margin: 0 auto;
            gap: 20px; 
        }

        .category-box {
            flex: 1;
            max-width: 240px;
            height: 240px;
            background-color: var(--loblolly);
            border-radius: 10px;
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease;
        }

        .category-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .category-box .see-more {
            position: absolute;
            bottom: 15px;
            right: 15px;
            padding: 8px 16px;
            background-color: var(--button-color);
            color: var(--white);
            font-weight: 700;
            text-decoration: none;
            border-radius: 5px;
            z-index: 2;
            transition: background-color 0.3s ease;
        }

        .category-box .see-more:hover {
            background-color: var(--hover-button-color);
        }

        .category-box:hover {
            transform: scale(1.05); /* Alleen de categorie-boxen vergroten bij hover */
        }

        @media screen and (max-width: 1024px) {
            .categories {
                flex-wrap: wrap;
                justify-content: center;
            }

            .category-box {
                flex: 1 1 45%;
                max-width: 45%;
            }
        }

        @media screen and (max-width: 768px) {
            .main-content-wrapper {
                padding: 20px;
            }

            .main-content h1 {
                font-size: 28px;
            }

            .main-content p {
                font-size: 14px;
            }

            .categories {
                flex-direction: column;
                gap: 20px;
            }

            .category-box {
                max-width: 100%;
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <a href="./HTML/Hoofdpagina.html" class="logo-link">
                <img src="./Afbeeldingen/logo.png" alt="SuppRadar Logo">
                <h1>SuppRadar.com</h1>
            </a>
        </div>
        <div class="nav-menu">
            <a href="./HTML/Hoofdpagina.html" class="active">Home</a>
            <a href="#">Supplementen</a>
            <div class="dropdown">
                <a href="#">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="#">Gewichtsverlies</a>
                    <a href="#">Spieropbouw</a>
                    <a href="#">Uithoudingsvermogen</a>
                    <a href="#">Fitness & Onderhoud</a>
                    <a href="#">Gezondheid & Focus</a>
                    <a href="#">Topprestaties</a>
                </div>
            </div>
            <a href="#">Accessoires</a>
            <a href="#">Oefeningen</a>
            <a href="#">Blog</a>
        </div>
    </div>

    <div class="content-container">
        <div class="main-content-wrapper">
            <div class="main-content">
                <h1>Boost je Fitnessreis met Doelgerichte Supplementen</h1>
                <p>Bij SuppRadar geloven we in het vinden van de beste supplementen die aansluiten bij jouw doelen, zonder dat we onze eigen producten promoten. Als onafhankelijke gids helpen we je om de topmerken en meest effectieve supplementen te ontdekken voor gewichtsverlies, spieropbouw, energieverbetering, en meer. We werken samen met betrouwbare leveranciers zodat jij de beste keuze kunt maken, gebaseerd op objectieve aanbevelingen en uitgebreide productvergelijkingen. Vertrouw op onze expertise om jouw fitnessreis te ondersteunen met eerlijke en betrouwbare informatie.</p>
                <a href="#" class="see-all-supplements">Bekijk Alle Supplementen</a>
            </div>
        </div>

        <div class="categories-section">
            <div class="see-categories">Bekijk Onze Categorieën</div>
            <div class="categories">
                <div class="category-box">
                    <a href="#">
                        <img src="./Afbeeldingen/Whey.png" alt="Proteïne">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="#">
                        <img src="./Afbeeldingen/Creatine.png" alt="Creatine">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="#">
                        <img src="./Afbeeldingen/Pre.png" alt="Pre-Workout">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
                <div class="category-box">
                    <a href="#">
                        <img src="./Afbeeldingen/Vitamines.png" alt="Vitaminen & Supplementen">
                        <span class="see-more">Bekijk Meer</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
