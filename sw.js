// Service Worker voor SuppRadar - Caching voor betere prestaties
const CACHE_NAME = 'suppradar-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/supplementen.html',
  '/calorie-calculator.html',
  '/blog.html',
  '/main.css',
  '/main.js',
  '/main.min.css',
  '/Afbeeldingen/Logo.webp',
  '/Afbeeldingen/Whey.webp',
  '/Afbeeldingen/Creatine.webp',
  '/Afbeeldingen/pre.webp',
  '/Afbeeldingen/vitamines.webp'
];

// Install event - cache resources
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Cache hit - return response
        if (response) {
          return response;
        }

        return fetch(event.request).then(
          function(response) {
            // Check if we received a valid response
            if(!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            var responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(function(cache) {
                cache.put(event.request, responseToCache);
              });

            return response;
          }
        );
      })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
