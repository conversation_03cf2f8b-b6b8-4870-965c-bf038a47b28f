:root {
    --black-pearl: #062034;
    --loblolly: #c5d1d5;
    --nevada: #64767e;
    --granny-smith: #7b949a;
    --river-bed: #495d62;
    --river-bed-alt: #44535c;
    --white: #ffffff;
    --button-color: #7b949a;
    --hover-button-color: #062034;
}

body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--white);
    color: var(--black-pearl);
    line-height: 1.8;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--white);
    padding: 15px 35px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between; /* Oorspronkelijke layout */
    align-items: center;
    position: relative;
    z-index: 1000;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
}

.logo-container img {
    max-width: 50px; /* Zorg ervoor dat het logo netjes naast de hamburger past */
    height: auto;
}


.logo-container h1 {
    font-size: 20px; /* Tekst iets kleiner voor balans */
    color: var(--black-pearl);
    margin: 0;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-menu a {
    color: var(--black-pearl);
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: color 0.3s ease, font-size 0.3s ease;
}

.nav-menu a:hover {
    color: var(--button-color);
    font-size: 18px;
}

.nav-menu a.active {
    color: var(--button-color);
}

.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    padding: 10px 0;
    border-radius: 5px;
    z-index: 1000;
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    color: var(--black-pearl);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-content a:hover {
    background-color: var(--loblolly);
    color: var(--white);
}

.dropdown:hover .dropdown-content {
    display: block;
}

.content-container {
    display: flex;
    gap: 20px;
    padding: 20px;
    align-items: stretch;
    flex-direction: row; /* Zorgt ervoor dat filter links naast de tabel komt */
}


.filter-menu {
    flex: 0 0 300px;
    background-color: var(--loblolly);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-section h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--black-pearl);
    border-bottom: 1px solid var(--nevada);
    padding-bottom: 5px;
}

.categories ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.categories ul li {
    display: flex;
    align-items: center;
}

.categories ul li input[type="checkbox"] {
    margin-right: 10px;
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.categories ul li span {
    font-size: 14px;
    color: var(--nevada);
}

.price-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-range {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price-range input[type="number"] {
    width: 60px;
    padding: 5px;
    border: 1px solid var(--nevada);
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

.price-range span {
    font-size: 18px;
    font-weight: 700;
    color: var(--black-pearl);
}

.rating-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-filter label {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-filter input[type="radio"] {
    accent-color: var(--button-color);
    transform: scale(1.2);
}

.rating-filter .stars {
    font-size: 14px;
    color: var(--granny-smith);
}

.product-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
    background-color: var(--white);
    border: 2px solid var(--nevada);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
	padding: 12px 20px;
    font-size: 16px;;
}

.product-table th,
.product-table td {
    padding: 15px;
    text-align: center;
    border: 1px solid var(--nevada);
	font-size: 14px;
}

.product-table th {
    background-color: var(--loblolly);
    font-size: 16px;
    font-weight: 700;
    color: var(--black-pearl);
}

.product-table td {
    font-size: 14px;
    color: var(--black-pearl);
}

.product-table img {
    width: 100%;
    height: auto;
    max-width: 250px;
    max-height: 260px;
    object-fit: cover;
    border-radius: 5px;
}

.product-table .button {
    padding: 10px 15px;
    background: var(--button-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.product-table .button:hover {
    background: var(--hover-button-color);
}

.footer {
    background-color: var(--river-bed-alt);
    padding: 20px;
    text-align: center;
    color: var(--white);
}

.footer-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 1080px;
    margin: 0 auto;
}

.footer-column {
    flex: 1;
    min-width: 200px;
    color: var(--loblolly);
}

.footer-column h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--white);
}

.footer-column ul {
    list-style: none;
    padding: 0;
}

.footer-column ul li a {
    color: var(--white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: var(--button-color);
}

.footer-bottom {
    margin-top: 20px;
    font-size: 14px;
    color: var(--loblolly);
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.social-icons img {
    width: 30px;
    height: 30px;
    transition: transform 0.3s ease;
}

.social-icons img:hover {
    transform: scale(1.1);
}

/* Zorg dat alle elementen goed werken met de breedte */
* {
    box-sizing: border-box;
}

/* Mobiele menu-weergave standaard verborgen */
.hamburger {
    display: none; /* Verberg hamburger op grotere schermen */
    font-size: 24px;
    cursor: pointer;
    margin: 0;
    padding: 5px;
    border: none;
    background: none;
    outline: none;
}

@media screen and (min-width: 769px) {
    .nav-menu {
        display: flex; /* Menu altijd zichtbaar op desktop */
    gap: 30px;
    align-items: center;
    }
}


    /* Mobiele navigatie standaard verborgen */
    .nav-menu {
        display: none;
        flex-direction: column;
        gap: 15px;
        background-color: var(--white);
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        padding: 10px 15px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Actieve menu-weergave bij klikken */
    .nav-menu.active {
        display: flex;
    }

    /* Responsieve lay-outaanpassing */
    .content-container {
        flex-direction: column;
        padding: 15px;
    }

    .filter-menu, .product-table-container {
        width: 100%;
        margin-bottom: 20px;
    }

    /* Afbeeldingen aanpassen */
    .product-table img {
        width: 100%;
        height: auto;
    }

    /* Footer aanpassen */
    .footer-container {
        flex-direction: column;
        text-align: center;
    }
	
	@media screen and (max-width: 768px) {
    .header {
        padding: 15px;
        flex-direction: row; /* Header behoudt flex-structuur */
    }
   .logo-container {
        margin-bottom: 20px;
        text-align: center;
		display: flex;
		margin: 0; /* Zorg dat er geen extra ruimte is */
    }

    .logo-container h1 {
        font-size: 24px;
        margin: 5px 0;
    }

    .nav-menu {
        flex-direction: column; /* Zet de links onder elkaar */
        gap: 20px; /* Extra ruimte tussen de links */
        align-items: center;
    }

    .nav-menu a {
        font-size: 18px; /* Maak de tekst groter */
        padding: 10px 0; /* Meer ruimte tussen de links */
    }

    .dropdown-content {
        padding: 10px; /* Meer ruimte in de dropdown-items */
    }

    .dropdown-content a {
        font-size: 16px; /* Iets grotere tekst in dropdown-items */
        padding: 8px 15px; /* Ruimte binnen de dropdown-links */
    }
}

	/* Hamburger-menu standaard styling */
	.hamburger {
		display: block;
		font-size: 24px; /* Iets kleiner zodat het netjes naast het logo past */
		cursor: pointer;
		margin: 0;
		padding: 5px;
		border: none;
		background: none;
		outline: none;
	}
.nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--white);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 15px;
        z-index: 1000;
    }

@media screen and (max-width: 768px) {
    /* Hamburger-menu tonen op mobiel */
    .hamburger {
        display: block;
    }

    /* Navigatiebalk standaard verborgen */
     .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--white);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 15px;
        z-index: 1000;
    }
	
    /* Navigatie zichtbaar wanneer geactiveerd */
    .nav-menu.active {
        display: flex;
    }

     .nav-menu a {
        font-size: 18px;
        text-align: center;
        padding: 10px 0;
    }
	
	@media screen and (min-width: 769px) {
    .header {
        justify-content: flex-start; /* Horizontale uitlijning */
        gap: 15px; /* Ruimte tussen logo en menu */
    }
}
	@media screen and (max-width: 768px) {
    .hamburger {
        display: block; /* Toon hamburger op mobiel */
    }
    .nav-menu {
        display: none; /* Verberg menu standaard op mobiel */
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--white);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 15px;
        z-index: 1000;
    }
    .nav-menu.active {
        display: flex; /* Toon menu als het actief is */
    }
}

}

}
